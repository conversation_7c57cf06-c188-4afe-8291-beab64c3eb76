{"@@locale": "en", "appTitle": "Self Eng", "@appTitle": {}, "home": "Home", "@home": {}, "library": "Library", "@library": {}, "search": "Search", "@search": {}, "games": "Games", "@games": {}, "profile": "Profile", "@profile": {}, "counterPage": "Counter page", "@counterPage": {}, "buttonPushedTimes": "You have pushed the button this many times:", "@buttonPushedTimes": {}, "increment": "Increment", "@increment": {}, "somethingWentWrong": "Something went wrong", "@somethingWentWrong": {}, "imageNotAvailable": "Image not available", "@imageNotAvailable": {}, "onboarding1": "Start Your English\nAdventure! 🚀", "@onboarding1": {}, "onboarding2": "Learn English,\nLimitless!🤳", "@onboarding2": {}, "onboarding3": "Get Started Now! ⏩", "@onboarding3": {}, "onboardingSingup": "Register with", "@onboardingSingup": {}, "signingIn": "Signing in...", "@signingIn": {}, "pleaseWait": "Please wait while we authenticate you", "@pleaseWait": {}, "selectLanguage": "Select a language", "@selectLanguage": {}, "selectLanguageDes": "Please select the language you want to use.", "@selectLanguageDes": {}, "languageIndo": "Bahasa Indonesia", "@languageIndo": {}, "languageEngl": "English", "@languageEngl": {}, "choose": "<PERSON><PERSON>", "@choose": {}, "other": "Other", "@other": {}, "next": "Next", "@next": {}, "previous": "Previous", "@previous": {}, "send": "Send", "@send": {}, "later": "Later", "@later": {}, "shortDescription": "Describe briefly and clearly", "@shortDescription": {}, "questionnaireOtherQ": "Mention your other main goals:", "@questionnaireOtherQ": {}, "questionnaireOtherA": "Type briefly", "@questionnaireOtherA": {}, "questionnaireFinish": "Thank you for completing the self-assessment questionnaire!", "@questionnaireFinish": {}, "questionnaireFinishDesc": "Your responses will help tailor the learning plan to meet your specific needs, interests, and goals. Let's begin this English learning journey together! 🚀📚✨🧑‍🤝‍🧑🌏", "@questionnaireFinishDesc": {}, "questionnaireIWilling": "Yes, let's begin", "@questionnaireIWilling": {}, "please_type": "Please type here", "@please_type": {}, "please_type_number": "Please type number", "@please_type_number": {}, "doIt": "Do it", "@doIt": {}, "instruction": "Instructions", "@instruction": {}, "testInstruction": "Test Instructions", "@testInstruction": {}, "testInstructionDesc": "Take a deep breath, focus, and read the instructions carefully before answering the questions", "@testInstructionDesc": {}, "testInstructionRule": "1. You have 15 minutes to complete the test.\n2. Read the questions and choose the correct answer.\n3. Choose the answer that best suits your understanding.\n4. Answer all questions, there is no penalty.\n5. The test automatically ends when time runs out.\n6. Do not use outside help.\n7. Get feedback after the test.", "@testInstructionRule": {}, "diagnosticTests": "Diagnostic Tests", "@diagnosticTests": {}, "areYouReady": "Are you ready?", "@areYouReady": {}, "yesIAmReady": "Yes, I am ready.", "@yesIAmReady": {}, "sorry_not_ready_yet": "Sorry, not ready yet.", "@sorry_not_ready_yet": {}, "areYouSure": "Are you sure?", "@areYouSure": {}, "yesIAmSure": "Yes, I am sure", "@yesIAmSure": {}, "notYetLater": "Not yet, later", "@notYetLater": {}, "continue1": "Continue", "@continue1": {}, "timesUpStudyGroup1": "Oh no, the test time is up 😔. Click the button ", "@timesUpStudyGroup1": {}, "timesUpStudyGroup2": "‘continue’", "@timesUpStudyGroup2": {}, "timesUpStudyGroup3": " to find out your study group", "@timesUpStudyGroup3": {}, "finallyResult": "Finally, your test results are out 🎉", "@finallyResult": {}, "processingTime": "Processing time", "@processingTime": {}, "totalScore": "Total Score", "@totalScore": {}, "learningLevel": "Learning Level", "@learningLevel": {}, "finallyResultDesc": "Happy learning, take your understanding to the next level, and enjoy every moment of this journey!", "@finallyResultDesc": {}, "questionnaireOnboarding1": "Welcome to the Diagnostic Test for the Integrated Self-Learning Speaking Program!", "@questionnaireOnboarding1": {}, "questionnaireOnboarding2": "This test aims to assess your English speaking proficiency at various stages", "@questionnaireOnboarding2": {}, "questionnaireOnboarding3": "Your performance in this test will help determine the level of the program that best suits your needs", "@questionnaireOnboarding3": {}, "start": "Start", "@start": {}, "point": "Point", "@point": {}, "level": "Level", "@level": {}, "information": "Information", "@information": {}, "questionnaireCongrat1": "Discover the world through the wonders of English!", "@questionnaireCongrat1": {}, "questionnaireCongrat1Desc": "Whether you're just starting your journey or aiming to reach a new level of fluency, now is the perfect time to begin.", "@questionnaireCongrat1Desc": {}, "questionnaireCongrat2": "Let's embark on this transformative adventure together,", "@questionnaireCongrat2": {}, "questionnaireCongrat2Desc": "An adventure where every word learned is one step closer to exciting opportunities and limitless experiences", "@questionnaireCongrat2Desc": {}, "questionnaireCongrat3": "Ignite your passion for English today.", "@questionnaireCongrat3": {}, "questionnaireCongrat3Desc": "Are you ready to take this journey? Let's dive in and make your English learning dreams come true! 📚🚀", "@questionnaireCongrat3Desc": {}, "pronunciationChallenge": "Pronunciation Challenge", "@pronunciationChallenge": {}, "conversationVideo": "Conversation Video", "@conversationVideo": {}, "listeningMastery": "Listening Mastery", "@listeningMastery": {}, "speakingArena": "Speaking Arena", "@speakingArena": {}, "record": "Record", "@record": {}, "stage1Speaking": "Listen and follow the audio and scripts", "stage1SpeakingDesc": "Listen to the audio and read the script carefully.", "@stage1SpeakingDesc": {}, "@stage1Speaking": {}, "stage2Speaking": "You act\nas the Questioner", "@stage2Speaking": {}, "stage3Speaking": "You act\nas the Re<PERSON>onder", "@stage3Speaking": {}, "nextSection": "Ready for the next challenge?", "@nextSection": {}, "hiUser": "Hi, {user<PERSON>ame} 👋", "@hiUser": {"description": "Welcome the user message", "placeholders": {"userName": {"type": "String", "example": "<PERSON>"}}}, "welcome": "Welcome!", "@welcome": {}, "welcome_back": "Welcome back! What's up?🥳", "@welcome_back": {}, "back": "Back", "@back": {}, "seeAll": "See all", "@seeAll": {}, "selectedLanguageDesc1": "You have chosen", "@selectedLanguageDesc1": {}, "express_yourself_in_english": "Ready to chat in English? Let's go! 💬", "@express_yourself_in_english": {}, "join_the_community": "Join community", "@join_the_community": {}, "instructions": "Instructions", "@instructions": {}, "listen_and_imitate": "Listen and Imitate", "@listen_and_imitate": {}, "listen_and_imitate_desc": "Listen to the pronunciation recording, focus on the sounds and stress, then imitate it out loud.", "@listen_and_imitate_desc": {}, "record_and_analyze": "Record and Improve", "@record_and_analyze": {}, "record_and_analyze_desc": "Record your pronunciation, get AI analysis, compare it with the model, and adjust until perfect.", "@record_and_analyze_desc": {}, "compare_and_adjust": "Compare and Adjust", "@compare_and_adjust": {}, "compare_and_adjust_desc": "Compare your pronunciation with the recorded model and make necessary adjustments based on the feedback.", "@compare_and_adjust_desc": {}, "practice_and_perfect": "Practice and Perfect", "@practice_and_perfect": {}, "practice_and_perfect_desc": "Keep practicing and refining your pronunciation until you feel confident in accurately pronouncing each word and expression.", "@practice_and_perfect_desc": {}, "watch_and_analyze": "Watch and Analyze", "@watch_and_analyze": {}, "watch_and_analyze_desc1": "Watch the video with subtitles on, repeat important parts, and focus on vocabulary, grammar, and conversation themes.", "@watch_and_analyze_desc1": {}, "watch_and_analyze_desc2": "Rewatch segments, pause to focus on specific parts, and break the conversation into smaller sections.", "@watch_and_analyze_desc2": {}, "focus_on_vocabulary_and_grammar": "Focus on vocabulary and grammar", "@focus_on_vocabulary_and_grammar": {}, "focus_on_vocabulary_and_grammar_desc1": "Use captions to reinforce understanding, vocabulary, and grammatical structures.", "@focus_on_vocabulary_and_grammar_desc1": {}, "focus_on_vocabulary_and_grammar_desc2": "Note unfamiliar words or phrases and their meanings.", "@focus_on_vocabulary_and_grammar_desc2": {}, "pronunciation_practice": "Pronunciation Practice", "@pronunciation_practice": {}, "pronunciation_practice_desc1": "Listen and mimic the pronunciation and intonation of the speaker.", "@pronunciation_practice_desc1": {}, "pronunciation_practice_desc2": "Pay attention to stress patterns, rhythm, and word connections, using the text as a visual aid.", "@pronunciation_practice_desc2": {}, "reflect_and_review": "Practice and Review", "@reflect_and_review": {}, "reflect_and_review_desc1": "Imitate the pronunciation, pay attention to the intonation patterns, and review the notes to improve areas that need enhancement.", "@reflect_and_review_desc1": {}, "reflect_and_review_desc2": "Review your notes and identify areas that need improvement, considering your language learning goals and interests.", "@reflect_and_review_desc2": {}, "listen_actively": "Listen and Take Notes", "@listen_actively": {}, "listen_actively_desc": "Use headphones/speakers, listen actively, and take notes to understand the recording.", "@listen_actively_desc": {}, "repeat_and_review": "Repeat and Review", "@repeat_and_review": {}, "repeat_and_review_desc": "Listen to the recording several times, pause and replay as needed.", "@repeat_and_review_desc": {}, "answer_the_questions": "Answer the Questions", "@answer_the_questions": {}, "answer_the_questions_desc": "Read the questions carefully and answer them based on the information provided in the conversation.", "@answer_the_questions_desc": {}, "submit_and_review": "Answer and Review", "@submit_and_review": {}, "submit_and_review_desc": "Choose the available answers, submit for evaluation, and review the feedback to improve your listening skills.", "@submit_and_review_desc": {}, "listen_and_follow": "Listen, Follow, and Record", "@listen_and_follow": {}, "listen_and_follow_desc": "Play the audio recording, follow the script to mimic the pronunciation and intonation, then record your voice.", "@listen_and_follow_desc": {}, "repeat_the_practice": "Compare and Improve", "@repeat_the_practice": {}, "repeat_the_practice_desc": "Compare your recording with the model audio, review the strengths and weaknesses, and keep practicing to improve your speaking skills.", "@repeat_the_practice_desc": {}, "record_and_compare": "Record and Compare", "@record_and_compare": {}, "record_and_compare_desc": "Record yourself while practicing and compare your recording with the audio model to identify differences.", "@record_and_compare_desc": {}, "evaluate_yourself": "Self Evaluation", "@evaluate_yourself": {}, "evaluate_yourself_desc": "Review your recording, reflect on your pronunciation, fluency, intonation, and pace to identify strengths and areas for improvement.", "@evaluate_yourself_desc": {}, "continuous_improvement": "Continuous Improvement", "@continuous_improvement": {}, "continuous_improvement_desc": "Practice and evaluate your performance consistently to improve your speaking skills.", "@continuous_improvement_desc": {}, "profile_settings": "Profile & Settings", "@profile_settings": {}, "edit_profile": "Edit Profile", "@edit_profile": {}, "settings": "Settings", "@settings": {}, "language": "Language", "@language": {}, "sound": "Sound", "@sound": {}, "dark_theme": "Dark Theme", "@dark_theme": {}, "membership": "Membership", "@membership": {}, "transaction_history": "Transaction History", "@transaction_history": {}, "logout": "Logout", "@logout": {}, "notification_settings": "Notification Settings", "@notification_settings": {}, "notification_preferences": "Notification Preferences", "@notification_preferences": {}, "general_notification": "General Notification", "@general_notification": {}, "general_notification_desc": "Receive essential updates and information regarding your account and our services.", "@general_notification_desc": {}, "promotion": "Promotion", "@promotion": {}, "promotion_desc": "Get notified about special offers, discounts, and exclusive deals to help you save.", "@promotion_desc": {}, "announcement": "Announcement", "@announcement": {}, "announcement_desc": "Stay informed about new features, important updates, and news about our services.", "@announcement_desc": {}, "study_reminder": "Study Reminder", "@study_reminder": {}, "study_reminder_desc": "Receive timely reminders for your scheduled study sessions to help you stay on track with your learning goals.", "@study_reminder_desc": {}, "notification_info": "You can change these settings anytime. Some notifications may still be delivered for important account or security updates.", "@notification_info": {}, "do_you_understand": "Do you understand?", "@do_you_understand": {}, "learning_progress": "Learning Progress", "@learning_progress": {}, "score_acquisition": "Score Acquisition", "@score_acquisition": {}, "excellent": "Excellent!", "@excellent": {}, "very_good": "Very good!👍", "@very_good": {}, "good": "Good!", "@good": {}, "be_better": "Can be better!💪", "@be_better": {}, "fair": "Fair enough!🙂", "@fair": {}, "yes": "Yes", "@yes": {}, "no": "No", "@no": {}, "how_to_answer": "Instruction", "@how_to_answer": {}, "cv_instruction_decs1": "Select a video from the list.", "@cv_instruction_decs1": {}, "cv_instruction_decs2": "The video will play automatically.", "@cv_instruction_decs2": {}, "cv_instruction_decs3": "Turn on Indonesian subtitles if needed.", "@cv_instruction_decs3": {}, "cv_instruction_decs4": "You can pause and adjust playback time.", "@cv_instruction_decs4": {}, "cv_instruction_decs5": "Enlarge the video to full screen.", "@cv_instruction_decs5": {}, "cv_instruction_decs6": "to exit full-ratio video view.", "@cv_instruction_decs6": {}, "cv_result": "Yay! You finally completed this video conversation section! 🎉", "@cv_result": {}, "click_the_button": "Click the button", "@click_the_button": {}, "pc_instruction_decs1": "to listen to the recording.", "@pc_instruction_decs1": {}, "pc_instruction_decs2": "to send recorded voice.", "@pc_instruction_decs2": {}, "pc_instruction_decs3a": "Button color changes", "@pc_instruction_decs3a": {}, "pc_instruction_decs3b": "to record your voice, then click back to send.", "@pc_instruction_decs3b": {}, "pc_instruction_decs4a": "Not holding the button", "@pc_instruction_decs4a": {}, "pc_instruction_decs4b": "in order to process the analysis of your voice recording.", "@pc_instruction_decs4b": {}, "pc_instruction_decs5a": "Don't forget to click the button", "@pc_instruction_decs5a": {}, "pc_instruction_decs5b": "in order to complete a series of challenges.", "@pc_instruction_decs5b": {}, "pc_instruction_decs6a": "You can also click the button", "@pc_instruction_decs6a": {}, "pc_instruction_decs6b": "if you are still hesitant in answering the challenge.", "@pc_instruction_decs6b": {}, "lm_instruction_decs1": "to play audio.", "@lm_instruction_decs1": {}, "lm_instruction_decs2": "to stop the audio.", "@lm_instruction_decs2": {}, "lm_instruction_decs3": "You can also replay the part of the audio you want to repeat.", "@lm_instruction_decs3": {}, "lm_instruction_decs4a": "Don't forget to click the button", "@lm_instruction_decs4a": {}, "lm_instruction_decs4b": "in order to complete a series of challenges.", "@lm_instruction_decs4b": {}, "lm_instruction_decs5a": "Also monitor your matrix", "@lm_instruction_decs5a": {}, "lm_instruction_decs5b": "while working on this challenge.", "@lm_instruction_decs5b": {}, "record_your_voice": "<PERSON><PERSON><PERSON> suara anda", "@record_your_voice": {}, "stage": "Stage", "@stage": {}, "is_logout_desc": "Do you want to exit this application?", "@is_logout_desc": {}, "repeat": "Repeat", "@repeat": {}, "evaluation_results": "Evaluation Results", "@evaluation_results": {}, "score_details": "Score Details", "@score_details": {}, "impressive_work": "Impressive work!🌟", "@impressive_work": {}, "bravo": "Bravo!👏", "@bravo": {}, "getting_closer": "Getting closer!🔜", "@getting_closer": {}, "tackling_a_tough_one": "Tackling a tough one!💪", "@tackling_a_tough_one": {}, "interesting_attempt": "Interesting attempt!😄", "@interesting_attempt": {}, "not_quite_there_yet": "Not quite there yet!🤔", "@not_quite_there_yet": {}, "keep_practicing": "Keep practicing!🔄", "@keep_practicing": {}, "great_job": "Great job!", "@great_job": {}, "good_effort": "Good effort!", "@good_effort": {}, "needs_improvement": "Needs improvement!", "@needs_improvement": {}, "accuracy": "Accuracy", "@accuracy": {}, "your_score": "Your score", "@your_score": {}, "vocabulary": "Vocabulary", "@vocabulary": {}, "part": "Part", "@part": {}, "exercise": "Exercise", "@exercise": {}, "your_answer_is_correct": "Your answer is correct! 🤩🤗", "@your_answer_is_correct": {}, "your_answer_is_wrong": "Your answer is wrong! 😫😭", "@your_answer_is_wrong": {}, "correct": "Correct", "@correct": {}, "wrong": "Incorrect", "@wrong": {}, "continueYourLessons": "Keep learning, crush your goals!📚", "@continueYourLessons": {}, "learning_material": "Finish it, can't wait to see the result! 🤩", "@learning_material": {}, "explore_your_potential": "Explore your potential! 🌍📱📚", "@explore_your_potential": {}, "unlock_opportunities": "Ready to unlock a world of opportunities?", "@unlock_opportunities": {}, "start_journey": "It's time to embark on a learning journey", "@start_journey": {}, "get_started": "Get Started", "@get_started": {}, "prosody": "Intonation & Rythm", "@prosody": {}, "completeness": "Completeness", "@completeness": {}, "unit": "Unit", "@unit": {}, "chapter_list": "Chapter List", "@chapter_list": {}, "more": "More", "@more": {}, "listening_exercise": "Listening Exercise", "@listening_exercise": {}, "skills_list": "Skills List", "@skills_list": {}, "explore_the_courses": "Explore the courses", "@explore_the_courses": {}, "level_pitch_sentences": "From basic communication to full mastery, these levels guide you on your way to English fluency.", "@level_pitch_sentences": {}, "remember_7_items": "Remember 7 items", "@remember_7_items": {}, "tap_items_you_saw": "Tap the items you saw", "@tap_items_you_saw": {}, "loading": "Loading", "@loading": {}, "ready": "Siap?", "@ready": {}, "go": "<PERSON><PERSON>!", "@go": {}, "memory_flash_result_desc": "You’ve done it! Let’s check your result 📋", "@memory_flash_result_desc": {}, "replay": "Replay", "@replay": {}, "topic_complete": "Topic Complete 📑", "@topic_complete": {}, "level_complete": "Level Complete🏅 ", "@level_complete": {}, "congratulations": "Congratulations! 🎉", "@congratulations": {}, "score": "Score", "@score": {}, "select_category": "Select Category", "@select_category": {}, "select_topic": "Select Topic", "@select_topic": {}, "please_wait": "Please wait", "@please_wait": {}, "complete_all_challenges": "Complete All Challenges", "@complete_all_challenges": {}, "complete_all_challenges_desc": "Please complete all challenges to view your final results.", "@complete_all_challenges_desc": {}, "gameplay": "Gameplay 🕹️", "@gameplay": {}, "how_to_play_memory_flash": "See 7 words for 3 seconds each, then pick them from a list to 10 ( 7 correct + 3 distractors).", "@how_to_play_memory_flash": {}, "cert_notif_a1": "👏 You’ve Completed the A1 Level! ", "@cert_notif_a1": {}, "cert_notif_a2": "🌟 Your English Skills Are Growing Fast!", "@cert_notif_a2": {}, "cert_notif_b1": "💪 Reaching B1 Means You Can Communicate with <PERSON><PERSON>!", "@cert_notif_b1": {}, "cert_notif_b2": "🚀 Completing B2 Shows Your Dedication!", "@cert_notif_b2": {}, "cert_notif_c1": "🔥 You’ve Reached Advanced Mastery!", "@cert_notif_c1": {}, "cert_notif_c2": "👑 You’ve Reached the Top!", "@cert_notif_c2": {}, "level_not_completed": "Level Not Completed", "@level_not_completed": {}, "level_not_completed_desc": "Please complete all chapters and sections in this level to unlock your certificate.", "@level_not_completed_desc": {}, "back_to_lessons": "Back to Lessons", "@back_to_lessons": {}, "cert_download_a1": "🎓 Download your A1 Certificate and show off your achievement!", "@cert_download_a1": {}, "cert_download_a2": "🎓 Grab your A2 Certificate and share your progress with the world!", "@cert_download_a2": {}, "cert_download_b1": "🎓 Download your B1 Certificate and inspire others to keep going!", "@cert_download_b1": {}, "cert_download_b2": "🎓 Show off your success — your B2 Certificate is ready!", "@cert_download_b2": {}, "cert_download_c1": "🎓 Download your C1 Certificate and mark your major achievement!", "@cert_download_c1": {}, "cert_download_c2": "🎓 Claim your C2 Mastery Certificate — you've earned it!", "@cert_download_c2": {}, "qualify_title_a1": "🚀 Master the basics!", "@qualify_title_a1": {}, "qualify_desc_a1": "50+ points in all skills unlocks your A1 certificate.", "@qualify_desc_a1": {}, "qualify_title_a2": "🎯 Boost your vocabulary & expressions!", "@qualify_title_a2": {}, "qualify_desc_a2": "Reach 50+ in listening and speaking for your A2 certificate.", "@qualify_desc_a2": {}, "qualify_title_b1": "🔥 Speak with confidence!", "@qualify_title_b1": {}, "qualify_desc_b1": "Get 50+ points in all skills to earn your B1 certificate.", "@qualify_desc_b1": {}, "qualify_title_b2": "🏆 Own the conversation!", "@qualify_title_b2": {}, "qualify_desc_b2": "Achieve 50+ points in all skills for your B2 certificate.", "@qualify_desc_b2": {}, "qualify_title_c1": "⚡ Show your mastery!", "@qualify_title_c1": {}, "qualify_desc_c1": "Score 50+ in all skills to unlock your C1 certificate.", "@qualify_desc_c1": {}, "qualify_title_c2": "🌟 Near native fluency!", "@qualify_title_c2": {}, "qualify_desc_c2": "Earn your C2 certificate with 50+ points in all skills.", "@qualify_desc_c2": {}, "process_loading": "Please wait a moment,\nyour work is still\nbeing processed 😊", "@process_loading": {}, "certificate_list": "Certificate List", "@certificate_list": {}, "recording_error": "Please try again, we are unable to hear your voice.", "@recording_error": {}, "chapter": "Chapter", "@chapter": {}, "chapters_range": "Chapters", "@chapters_range": {}, "needs_practice": "Needs Practice!", "@needs_practice": {}, "needs_practice_desc": "Speech is unclear and uneven.", "@needs_practice_desc": {}, "sound_match": "🔊 Sound Match", "@sound_match": {}, "smooth_talk": "💬 Smooth Talk", "@smooth_talk": {}, "natural_flow": "🎶 Natural Flow", "@natural_flow": {}, "excellent_desc": "Smooth, accurate, and natural.", "@excellent_desc": {}, "good_desc": "Clear, but still room to improve.", "@good_desc": {}, "share": "Share", "@share": {}, "certificates": "Certificates", "@certificates": {}, "no_certificates_found": "No certificates found.", "@no_certificates_found": {}, "certificate": "Certificate", "@certificate": {}, "certificate_detail": "Certificate Detail", "@certificate_detail": {}, "no_certificate_data_available": "No certificate data available", "@no_certificate_data_available": {}, "no_certificate_selected": "No certificate selected", "@no_certificate_selected": {}, "certificate_pages": "Certificate Pages", "@certificate_pages": {}, "page_1": "Page 1", "@page_1": {}, "page_2": "Page 2", "@page_2": {}, "view_details": "View Details", "@view_details": {}, "download": "Download", "@download": {}, "share_all_pages": "Share All Pages", "@share_all_pages": {}, "issued_on": "Issued on {date}", "@issued_on": {"placeholders": {"date": {"type": "String"}}}, "level_certificate": "{level} Certificate", "@level_certificate": {"placeholders": {"level": {"type": "String"}}}, "check_out_my_certificate": "Check out my {level} certificate!", "@check_out_my_certificate": {"placeholders": {"level": {"type": "String"}}}, "check_out_my_certificates": "Check out my {level} certificates!", "@check_out_my_certificates": {"placeholders": {"level": {"type": "String"}}}, "my_certificate": "My Certificate", "@my_certificate": {}, "my_certificates": "My Certificates", "@my_certificates": {}, "certificate_shared_successfully": "Certificate shared successfully", "@certificate_shared_successfully": {}, "certificates_shared_successfully": "Certificates shared successfully", "@certificates_shared_successfully": {}, "sharing_cancelled_or_failed": "Sharing was cancelled or failed", "@sharing_cancelled_or_failed": {}, "share_failed": "Share failed: {error}", "@share_failed": {"placeholders": {"error": {"type": "String"}}}, "download_failed": "Download failed: {error}", "@download_failed": {"placeholders": {"error": {"type": "String"}}}, "unable_to_access_downloads_folder": "Unable to access Downloads folder", "@unable_to_access_downloads_folder": {}, "failed_to_convert_certificate_to_png": "Failed to convert certificate to PNG", "@failed_to_convert_certificate_to_png": {}, "certificate_saved_to_downloads": "Certificate saved to Downloads folder", "@certificate_saved_to_downloads": {}, "certificate_saved_to_files_app": "Certificate saved to Files app", "@certificate_saved_to_files_app": {}, "failed_to_load_certificates": "Failed to load certificates. Please try again.", "@failed_to_load_certificates": {}, "urls_and_filenames_mismatch": "URLs and file names count mismatch", "@urls_and_filenames_mismatch": {}, "check_out_my_certificate_generic": "Check out my certificate!", "@check_out_my_certificate_generic": {}, "check_out_my_certificates_generic": "Check out my certificates!", "@check_out_my_certificates_generic": {}, "bookmarks": "Bookmarks", "@bookmarks": {}, "lm_100_desc": "You're ready\nfor the next level! ⏫", "@lm_100_desc": {}, "lm_50_desc": "Come on, step on the gas\nand it'll go smoother! 🚀", "@lm_50_desc": {}, "lm_0_desc": "Keep practicing,\nyou'll get better! 💪", "@lm_0_desc": {}, "correct_emot": "Correct ✅", "@correct_emot": {}, "retake_certificate": "Retake Certificate", "@retake_certificate": {}, "retake_certificate_warning_title": "Retake Certificate Warning", "@retake_certificate_warning_title": {}, "retake_certificate_warning_message": "Retaking this certificate will reset all your progress for the current level. Only the last 3 certificates will be kept in your history.", "@retake_certificate_warning_message": {}, "retake_certificate_success": "Your progress has been reset successfully. You can now attempt to retake the certificate.", "@retake_certificate_success": {}, "retake_certificate_error": "Failed to reset your progress. Please try again.", "@retake_certificate_error": {}, "cancel": "Cancel", "@cancel": {}, "no_certificates_yet": "You don't have any certificates yet", "@no_certificates_yet": {}, "every_expert_was_beginner": "Every expert was once a beginner. Start your English learning journey today and earn your first certificate!", "@every_expert_was_beginner": {}, "complete_lessons_practice": "Complete lessons, practice regularly, and watch your progress grow!", "@complete_lessons_practice": {}, "start_learning_now": "Start Learning Now", "@start_learning_now": {}, "incorrect_emot": "Incorrect ❌", "@incorrect_emot": {}, "install": "Install", "@install": {"description": "Button label to install the downloaded update"}, "installUpdateTitle": "Install Update", "@installUpdateTitle": {"description": "Dialog title asking user to install update"}, "installUpdateDescription": "The app will restart to apply the update. Any unsaved changes will be lost.", "@installUpdateDescription": {"description": "Dialog description explaining the update installation will restart the app"}, "updateReadyToInstall": "Update Ready to Install", "@updateReadyToInstall": {"description": "Banner title indicating update is downloaded and ready"}, "tapToRestartAndApplyUpdate": "Tap to restart and apply the update", "@tapToRestartAndApplyUpdate": {"description": "Banner subtitle prompting user action"}}