import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/library/presentation/providers/chapter_content_provider.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/domain/repositories/main_lesson_repository.dart';
import 'package:selfeng/features/main_lesson/domain/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/main_lesson_provider.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/main_lesson_state.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/services/user_data_service/domain/providers/user_data_service_provider.dart';
import 'package:selfeng/services/user_data_service/domain/repositories/user_data_service_repository.dart';
import 'package:selfeng/shared/domain/models/either.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/exceptions/http_exception.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';

part 'speaking_controller.g.dart';

/// This controller is an [AsyncNotifier] that holds and handles our speaking state
@riverpod
class SpeakingController extends _$SpeakingController {
  late MainLessonRepository mainLessonRepository;
  late UserDataServiceRepository _userDataServiceRepository;
  List<ContentIndexData> paths = [];
  Set<String> _stageOneCompletedPaths = {};
  Set<String> _stageTwoCompletedPaths = {};
  Set<String> _stageThreeCompletedPaths = {};
  late MainLessonState _mainLessonState;
  late ChapterContentStateNotifier _chapterContentStateNotifier;

  @override
  FutureOr<SpeakingState> build(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) async {
    mainLessonRepository = ref.watch(mainLessonRepositoryProvider);
    _userDataServiceRepository = ref.watch(userDataServiceProvider);
    _chapterContentStateNotifier = ref.watch(
      chapterContentStateProvider.notifier,
    );

    await mainLessonRepository.isIntro(lessonName: 'speaking').then((val) {
      val.fold(
        (failure) async {
          // Report the error to Crashlytics with context
          final crashlytics = ref.read(crashlyticsServiceProvider);
          await crashlytics
              .recordError(
                failure,
                StackTrace.current,
                reason: 'Speaking Intro Check Failed',
                context: {
                  'category': 'speaking_lesson',
                  'operation': 'check_intro',
                  'lesson_name': 'speaking',
                  'level': level,
                  'chapter': chapter,
                  'path': path,
                  'stage': stage?.name ?? 'unknown',
                },
                fatal: false,
              )
              .catchError((e) => debugPrint('Failed to report error: $e'));

          state = AsyncError(failure.message, StackTrace.current);
        },
        (data) async {
          // isIntro value is handled in the init method if needed
        },
      );
    });

    // For result screen (stage3), we need to ensure aggregate results are calculated
    // before returning the initial state to prevent UI flicker
    if (stage == SpeakingStage.stage3) {
      // Initialize with a temporary state to prevent null access
      state = AsyncData(SpeakingState());
      await init(level, chapter, path, stage);

      // Calculate aggregate results before returning the state
      await calculateAgregate(SpeakingStage.stage2);
      await calculateAgregate(SpeakingStage.stage3);

      // Small delay to ensure state is fully stabilized before UI renders
      await Future.delayed(const Duration(milliseconds: 50));
    } else {
      // For other stages, initialize normally
      state = AsyncData(SpeakingState());
      await init(level, chapter, path, stage);
    }

    return state.value ?? SpeakingState();
  }

  Future<void> init(
    String level,
    String chapter,
    String path,
    SpeakingStage? stage,
  ) async {
    final contents = await mainLessonRepository.getPathIndex(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
    );
    await mainLessonRepository.saveIntro(lessonName: 'speaking');
    contents.fold(
      (failure) async {
        // Report path index error to Crashlytics
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Path Index Fetch Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'get_path_index',
                'level': level,
                'chapter': chapter,
                'path': path,
                'stage': stage?.name ?? 'unknown',
                'section': 'speaking',
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.current);
      },
      (data) async {
        paths = data;

        final speakingResultsFutures = [
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage1,
          ),
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage2,
          ),
          _userDataServiceRepository.getSpeakingResult(
            level: level,
            chapter: chapter,
            stage: SpeakingStage.stage3,
          ),
        ];

        final speakingResults = await Future.wait(speakingResultsFutures);

        final completedPaths =
            speakingResults.map((result) {
              return result.fold((failure) {
                // Report speaking results fetch error (synchronously)
                Future.microtask(() async {
                  final crashlytics = ref.read(crashlyticsServiceProvider);
                  await crashlytics
                      .recordError(
                        failure,
                        StackTrace.current,
                        reason: 'Speaking Results Fetch Failed',
                        context: {
                          'category': 'speaking_lesson',
                          'operation': 'get_speaking_results',
                          'level': level,
                          'chapter': chapter,
                        },
                        fatal: false,
                      )
                      .catchError(
                        (e) => debugPrint('Failed to report error: $e'),
                      );
                });

                state = AsyncError(failure.message, StackTrace.current);
                return null;
              }, (data) => data.map((e) => e.path).toSet());
            }).toList();

        if (completedPaths.contains(null)) {
          return;
        }

        _stageOneCompletedPaths = completedPaths[0] ?? <String>{};
        _stageTwoCompletedPaths = completedPaths[1] ?? <String>{};
        _stageThreeCompletedPaths = completedPaths[2] ?? <String>{};

        _mainLessonState = ref.read(mainLessonStateProvider);
        if (_mainLessonState.fromLastCourse == true || path != 'blankpath') {
          state = AsyncData(state.value!.copyWith(showStageOnboarding: false));
        } else {
          state = AsyncData(state.value!.copyWith(showStageOnboarding: true));
        }

        if (_mainLessonState.fromLastCourse == false && path != 'blankpath') {
          final contentPath = utf8.decode(base64Url.decode(path));
          final idx = paths.indexWhere(
            (element) => element.contentPath == contentPath,
          );
          if (idx != -1) {
            state = AsyncData(
              state.value!.copyWith(
                selectedIndex: idx,
                speakingStage: stage ?? SpeakingStage.stage1,
              ),
            );
          }
        } else if (_mainLessonState.fromLastCourse == true &&
            _mainLessonState.lastSpeaking != null) {
          final idx = paths.indexWhere(
            (element) =>
                element.contentPath == _mainLessonState.lastSpeaking!.path,
          );
          if (idx != -1) {
            state = AsyncData(
              state.value!.copyWith(
                selectedIndex: idx,
                speakingStage: _mainLessonState.lastSpeaking!.speakingStage,
              ),
            );
          }
        }
        // Log successful initialization
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics.setCustomKeys({
          'speaking_lesson_level': level,
          'speaking_lesson_chapter': chapter,
          'speaking_lesson_stage': stage?.name ?? 'initial',
          'speaking_paths_count': paths.length,
          'stage1_completed_count': _stageOneCompletedPaths.length,
          'stage2_completed_count': _stageTwoCompletedPaths.length,
          'stage3_completed_count': _stageThreeCompletedPaths.length,
        });
        crashlytics.log(
          'Speaking lesson initialized - Level: $level, Chapter: $chapter, Stage: ${stage?.name ?? "initial"}, Paths: ${paths.length}',
        );

        await initContent();
      },
    );
  }

  Future<void> initContent() async {
    final contentFuture = mainLessonRepository.getSpeakingList(
      paths.map((e) => e.contentPath).toList(),
    );

    final bookmarkFuture = _userDataServiceRepository.getBookmarksBySection(
      section: SectionType.speaking,
    );

    final results = await Future.wait([contentFuture, bookmarkFuture]);

    final contentResult =
        results[0] as Either<AppException, List<SpeakingPart>>;
    final bookmarkResult = results[1] as Either<AppException, List<Bookmark>>;

    if (contentResult.isLeft()) {
      final failure = contentResult.fold((l) => l, (r) => null);

      // Report content fetch error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            failure!,
            StackTrace.current,
            reason: 'Speaking Content Fetch Failed',
            context: {
              'category': 'speaking_lesson',
              'operation': 'get_speaking_content',
              'paths_count': paths.length,
              'error_type': failure.runtimeType.toString(),
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      state = AsyncError(failure.message, StackTrace.current);
      return;
    }
    if (bookmarkResult.isLeft()) {
      final failure = bookmarkResult.fold((l) => l, (r) => null);

      // Report bookmark fetch error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            failure!,
            StackTrace.current,
            reason: 'Speaking Bookmarks Fetch Failed',
            context: {
              'category': 'speaking_lesson',
              'operation': 'get_bookmarks',
              'section': 'speaking',
              'error_type': failure.runtimeType.toString(),
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      state = AsyncError(failure.message, StackTrace.current);
      return;
    }

    List<SpeakingPart> contentData = contentResult.fold((l) => null, (r) => r)!;
    final List<Bookmark> bookmarkData =
        bookmarkResult.fold((l) => null, (r) => r)!;

    for (int i = 0; i < contentData.length; i++) {
      final item = contentData[i];
      final isBookmarked = bookmarkData.any(
        (bookmark) =>
            bookmark.path == paths[i].contentPath && bookmark.isBookmarked,
      );
      contentData[i] = item.copyWith(isBookmarked: isBookmarked);
    }

    state = AsyncData(state.value!.copyWith(speakings: contentData));

    if (contentData.isNotEmpty) {
      if (state.value?.speakingStage == SpeakingStage.stage2) {
        setQAActive(session: SpeakingSessionType.question);
      } else {
        setQAActive(session: SpeakingSessionType.question, isListening: true);
      }
      saveLastCourse();
    }
  }

  void setQAActive({
    SpeakingSessionType session = SpeakingSessionType.question,
    bool isListening = false,
  }) {
    List<SpeakingPart> tempData = List.from(state.value!.speakings);
    tempData[state.value!.selectedIndex] = tempData[state.value!.selectedIndex]
        .copyWith(
          question: tempData[state.value!.selectedIndex].question.copyWith(
            isActive: session == SpeakingSessionType.question ? true : false,
          ),
          answer: tempData[state.value!.selectedIndex].answer.copyWith(
            isActive: session == SpeakingSessionType.answer ? true : false,
          ),
        );
    state = AsyncData(
      state.value!.copyWith(speakings: tempData, isListening: isListening),
    );
  }

  Future<void> uploadAudio({required String path}) async {
    try {
      // Log audio upload
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics.log('Audio uploaded for pronunciation check - Path: $path');
      await crashlytics.setCustomKey(
        'last_audio_upload',
        DateTime.now().toIso8601String(),
      );

      state = AsyncData(
        state.value!.copyWith(audioPath: AudioPath(path: path)),
      );
      checkPronunciation();
    } catch (error, stackTrace) {
      // Report upload error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Audio Upload Failed',
            context: {
              'category': 'speaking_lesson',
              'operation': 'upload_audio',
              'audio_path': path,
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      rethrow;
    }
  }

  Future<void> checkPronunciation() async {
    String text = '';
    switch (state.value?.speakingStage) {
      case SpeakingStage.stage2:
        text = state.value!.speakings[state.value!.selectedIndex].question.text;
        break;
      case SpeakingStage.stage3:
        text = state.value!.speakings[state.value!.selectedIndex].answer.text;
        break;
      default:
    }
    state = AsyncData(state.value!.copyWith(isLoading: true));
    final result = await mainLessonRepository.checkPronunciation(
      audio: state.value!.audioPath!,
      text: text,
    );
    result.fold(
      (failure) async {
        // Report pronunciation check error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Pronunciation Check Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'check_pronunciation',
                'speaking_stage': state.value!.speakingStage.name,
                'text_length': text.length,
                'audio_path': state.value!.audioPath!.path,
                'selected_index': state.value!.selectedIndex,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncData(
          state.value!.copyWith(isLoading: false, response: null),
        );
        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) async {
        // Log successful pronunciation check
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics.setCustomKeys({
          'last_pronunciation_check': DateTime.now().toIso8601String(),
          'speaking_stage': state.value!.speakingStage.name,
        });
        crashlytics.log(
          'Pronunciation check completed successfully - Stage: ${state.value!.speakingStage.name}, Score: ${data.pronScore}',
        );

        state = AsyncData(
          state.value!.copyWith(response: data, isLoading: false),
        );

        await saveResult();
      },
    );
  }

  void changeStage(SpeakingStage stage) {
    state = AsyncData(
      state.value!.copyWith(
        speakingStage: stage,
        response: null,
        nextSection: false,
        selectedIndex: 0,
      ),
    );
    if (state.value?.speakingStage == SpeakingStage.stage2) {
      setQAActive(isListening: false);
    } else {
      setQAActive(isListening: true);
    }
  }

  void nextStage() {
    if (state.value?.speakingStage == SpeakingStage.stage1 &&
        state.value!.nextSection) {
      changeStage(SpeakingStage.onboardingStage2);
    } else if (state.value?.speakingStage == SpeakingStage.stage2 &&
        state.value!.nextSection) {
      changeStage(SpeakingStage.onboardingStage3);
    }
  }

  Future<void> prevQuestion() async {
    if (state.value!.selectedIndex > 0) {
      state = AsyncData(
        state.value!.copyWith(
          selectedIndex: state.value!.selectedIndex - 1,
          response: null,
          // stageTalking: 'stage 1',
        ),
      );
    }
  }

  Future<void> nextQuestion(BuildContext context) async {
    try {
      SpeakingStage speakingStage = state.value!.speakingStage;
      if (state.value!.selectedIndex < state.value!.speakings.length - 1) {
        state = AsyncData(
          state.value!.copyWith(
            selectedIndex: state.value!.selectedIndex + 1,
            response: null,
            // stageTalking: 'stage 1',
          ),
        );
        if (speakingStage == SpeakingStage.stage1) {
          saveStageOne();
        }
        if (speakingStage == SpeakingStage.stage2) {
          setQAActive(session: SpeakingSessionType.question);
        } else {
          setQAActive(session: SpeakingSessionType.question, isListening: true);
        }
        if (speakingStage != SpeakingStage.stage1) {
          // Save the current state before navigation
          await saveLastCourse();
          // Set fromLastCourse to true so the next screen uses lastSpeaking data
          final mainLessonStateNotifier = ref.watch(
            mainLessonStateProvider.notifier,
          );
          mainLessonStateNotifier.updateFromLastCourse(true);

          customNav(
            context,
            RouterName.speakingArenaStage,
            isReplace: true,
            params: {
              'level': level,
              'chapter': chapter,
              'path': path,
              'stage': state.value!.speakingStage.name,
            },
          );
        } else {
          saveLastCourse();
        }
      } else {
        state = AsyncData(state.value!.copyWith(nextSection: true));
        if (state.value?.speakingStage == SpeakingStage.stage3) {
          await calculateAgregate(SpeakingStage.stage2);
          await calculateAgregate(SpeakingStage.stage3);
          customNav(
            context,
            RouterName.speakingArenaResult,
            isReplace: true,
            params: {
              'level': level,
              'chapter': chapter,
              'path': path,
              'stage': state.value!.speakingStage.name,
            },
          );
        } else {
          saveStageOne();
          nextStage();
          customNav(
            context,
            RouterName.speakingArenaStage,
            isReplace: true,
            params: {
              'level': level,
              'chapter': chapter,
              'path': path,
              'stage': state.value!.speakingStage.name,
            },
          );
        }
      }
    } catch (error, stackTrace) {
      // Report navigation/next question error
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics
          .recordError(
            error,
            stackTrace,
            reason: 'Next Question Navigation Failed',
            context: {
              'category': 'speaking_lesson',
              'operation': 'next_question',
              'speaking_stage': state.value?.speakingStage.name ?? 'unknown',
              'selected_index': state.value?.selectedIndex ?? -1,
              'total_speakings': state.value?.speakings.length ?? 0,
            },
            fatal: false,
          )
          .catchError((e) => debugPrint('Failed to report error: $e'));

      rethrow;
    }
  }

  Future<void> saveLastCourse() async {
    final data = LastCourse(
      accessTime: DateTime.now().toUtc(),
      level: level,
      chapter: int.parse(chapter),
      section: SectionType.speaking,
      path: paths[state.value!.selectedIndex].contentPath,
      speakingStage: state.value!.speakingStage,
    );

    await _userDataServiceRepository.updateLastCourse(
      lastCourse: data,
      section: SectionType.speaking,
    );
    final mainLessonStateNotifier = ref.watch(mainLessonStateProvider.notifier);
    mainLessonStateNotifier.updateLastSpeaking(data);
    mainLessonStateNotifier.updateFromLastCourse(false);
  }

  Future<void> calculateAgregate(SpeakingStage stage) async {
    final result = await _userDataServiceRepository.calculateSpeakingResult(
      level: level,
      chapter: chapter,
      stage: stage,
    );

    result.fold(
      (failure) async {
        // Report aggregate calculation error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Aggregate Calculation Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'calculate_aggregate',
                'stage': stage.name,
                'level': level,
                'chapter': chapter,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        if (stage == SpeakingStage.stage2) {
          state = AsyncData(state.value!.copyWith(resultStage2: data));
        } else if (stage == SpeakingStage.stage3) {
          state = AsyncData(state.value!.copyWith(resultStage3: data));
        }
      },
    );
  }

  Future<void> saveResult() async {
    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
      result: LessonResult(
        contentOrder: paths[state.value!.selectedIndex].contentOrder,
        speakingStage: state.value!.speakingStage,
        path: paths[state.value!.selectedIndex].contentPath,
        result: {
          'accuracyScore': state.value!.response!.accuracyScore,
          'fluencyScore': state.value!.response!.fluencyScore,
          'prosodyScore': state.value!.response!.prosodyScore,
          'completenessScore': state.value!.response!.completenessScore,
          'pronScore': state.value!.response!.pronScore,
        },
      ),
    );

    result.fold(
      (failure) async {
        // Report save result error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Result Save Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'save_result',
                'level': level,
                'chapter': chapter,
                'speaking_stage': state.value!.speakingStage.name,
                'content_order': paths[state.value!.selectedIndex].contentOrder,
                'path': paths[state.value!.selectedIndex].contentPath,
                'accuracy_score': state.value!.response!.accuracyScore,
                'fluency_score': state.value!.response!.fluencyScore,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) async {
        // Log successful result save
        final crashlytics = ref.read(crashlyticsServiceProvider);
        crashlytics.log(
          'Speaking result saved - Stage: ${state.value!.speakingStage.name}, Path: ${paths[state.value!.selectedIndex].contentPath}',
        );

        if (state.value!.speakingStage == SpeakingStage.stage2) {
          _stageTwoCompletedPaths.add(
            paths[state.value!.selectedIndex].contentPath,
          );
          // Track stage 2 completion
          await crashlytics.setCustomKey(
            'stage2_completions',
            _stageTwoCompletedPaths.length,
          );
        } else if (state.value!.speakingStage == SpeakingStage.stage3) {
          _stageThreeCompletedPaths.add(
            paths[state.value!.selectedIndex].contentPath,
          );
          // Track stage 3 completion
          await crashlytics.setCustomKey(
            'stage3_completions',
            _stageThreeCompletedPaths.length,
          );
        }
        updateHasResult();
        markSectionAsCompleted();
      },
    );
  }

  Future<void> saveStageOne() async {
    final result = await _userDataServiceRepository.saveLessonResult(
      level: level,
      chapter: chapter,
      section: SectionType.speaking,
      result: LessonResult(
        contentOrder: paths[state.value!.selectedIndex].contentOrder,
        speakingStage: SpeakingStage.stage1,
        path: paths[state.value!.selectedIndex].contentPath,
        result: {
          'accuracyScore': 0,
          'fluencyScore': 0,
          'prosodyScore': 0,
          'completenessScore': 0,
          'pronScore': 0,
        },
      ),
    );

    result.fold(
      (failure) async {
        // Report save stage one error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Stage One Save Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'save_stage_one',
                'level': level,
                'chapter': chapter,
                'content_order': paths[state.value!.selectedIndex].contentOrder,
                'path': paths[state.value!.selectedIndex].contentPath,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        _stageOneCompletedPaths.add(
          paths[state.value!.selectedIndex].contentPath,
        );
        updateHasResult();
        markSectionAsCompleted();
      },
    );
  }

  Future<void> markSectionAsCompleted() async {
    // Use set operations for O(n) performance instead of O(n*m) for each stage
    // Create a set of required paths and check if all stages have completed all paths
    final requiredPaths = paths.map((pathData) => pathData.contentPath).toSet();

    final stageOneComplete =
        requiredPaths.difference(_stageOneCompletedPaths).isEmpty;
    final stageTwoComplete =
        requiredPaths.difference(_stageTwoCompletedPaths).isEmpty;
    final stageThreeComplete =
        requiredPaths.difference(_stageThreeCompletedPaths).isEmpty;

    if (stageOneComplete && stageTwoComplete && stageThreeComplete) {
      // Log section completion
      final crashlytics = ref.read(crashlyticsServiceProvider);
      crashlytics.log(
        'Speaking section completed - Level: $level, Chapter: $chapter',
      );
      await crashlytics.setCustomKeys({
        'last_section_completed': 'speaking',
        'completed_level': level,
        'completed_chapter': chapter,
        'section_completed_at': DateTime.now().toIso8601String(),
      });

      await _userDataServiceRepository.setSectionCompleted(
        level: level,
        chapter: chapter,
        section: SectionType.speaking,
      );
    }
  }

  void updateHasResult() {
    _chapterContentStateNotifier.updateSpeaking(
      paths[state.value!.selectedIndex],
      state.value!.speakingStage,
    );
  }

  void clearResponse() {
    state = AsyncData(state.value!.copyWith(response: null));
  }

  Future<void> saveBookmark() async {
    bool isBookmarked =
        !state.value!.speakings[state.value!.selectedIndex].isBookmarked;

    final String partOrder =
        (paths[state.value!.selectedIndex].partOrder ?? 0).toString();
    final String subpartOrder =
        (paths[state.value!.selectedIndex].subpartOrder ?? 0).toString();
    final String contentOrder =
        (paths[state.value!.selectedIndex].contentOrder).toString();
    final String speakingStage = '';
    //(_paths[state.value!.currentPage].speakingStage?.name ?? '');

    final String docId =
        '$level$chapter$partOrder$subpartOrder$contentOrder$speakingStage';
    final result = await _userDataServiceRepository.saveBookmark(
      section: SectionType.speaking,
      content: Bookmark(
        docId: docId,
        path: paths[state.value!.selectedIndex].contentPath,
        isBookmarked: isBookmarked,
      ),
    );

    result.fold(
      (failure) async {
        // Report bookmark save error
        final crashlytics = ref.read(crashlyticsServiceProvider);
        await crashlytics
            .recordError(
              failure,
              StackTrace.current,
              reason: 'Speaking Bookmark Save Failed',
              context: {
                'category': 'speaking_lesson',
                'operation': 'save_bookmark',
                'section': 'speaking',
                'path': paths[state.value!.selectedIndex].contentPath,
                'doc_id': docId,
                'is_bookmarked': isBookmarked,
                'error_type': failure.runtimeType.toString(),
              },
              fatal: false,
            )
            .catchError((e) => debugPrint('Failed to report error: $e'));

        state = AsyncError(failure.message, StackTrace.empty);
      },
      (data) {
        var speakings = state.value!.speakings;
        speakings = List.from(speakings);
        speakings[state.value!.selectedIndex] = speakings[state
                .value!
                .selectedIndex]
            .copyWith(isBookmarked: isBookmarked);
        // Update the current state with the new bookmark status
        state = AsyncData(state.value!.copyWith(speakings: speakings));
        paths[state.value!.selectedIndex] = paths[state.value!.selectedIndex]
            .copyWith(isBookmarked: isBookmarked);
        _chapterContentStateNotifier.updateSpeaking(
          paths[state.value!.selectedIndex],
          state.value!.speakingStage,
        );
      },
    );
  }

  void translate() {
    state = AsyncData(
      state.value!.copyWith(isTranslate: !state.value!.isTranslate),
    );
  }
}
