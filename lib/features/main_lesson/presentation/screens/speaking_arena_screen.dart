import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/main_lesson_app_bar.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/sound_wave_audio_player.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/widgets.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/dash_progress_indicator.dart';

class SpeakingArenaScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;

  const SpeakingArenaScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
  });

  @override
  ConsumerState<SpeakingArenaScreen> createState() =>
      _SpeakingArenaScreenState();
}

class _SpeakingArenaScreenState extends ConsumerState<SpeakingArenaScreen>
    with TickerProviderStateMixin {
  late AsyncValue<SpeakingState> viewState;
  late SpeakingController viewModel;
  final AudioPlayer _player = AudioPlayer();
  late final AnimationController _animationController;
  late final Animation<double> _animation;
  bool _isAnimationComplete = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimation();
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    )..addStatusListener(_handleAnimationStatus);
    _animationController.forward();
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed && !_isAnimationComplete) {
      _animationController.reverse();
    } else if (status == AnimationStatus.dismissed) {
      setState(() => _isAnimationComplete = true);
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _player.stop();
    _player.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final prov = speakingControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      SpeakingStage.stage1,
    );
    viewState = ref.watch(prov);
    viewModel = ref.watch(prov.notifier);

    // Listener for errors
    ref.listen(prov.select((value) => value), ((previous, next) {
      next.maybeWhen(
        error: (error, track) {
          if (mounted) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text(error.toString())));
          }
        },
        orElse: () {},
      );
    }));

    return switch (viewState) {
      AsyncData(:final value) => _buildContent(context, value),
      AsyncError(:final error) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text('An error occurred: $error'),
          ),
        ),
      ),
      AsyncLoading() => const Scaffold(body: AppLoading()),
      _ => const Scaffold(body: Center(child: Text('Loading...'))),
    };
  }

  Widget _buildContent(BuildContext context, SpeakingState state) {
    if (state.showStageOnboarding == true) {
      return FadeTransition(
        opacity: _animation,
        child:
            _isAnimationComplete
                ? _buildStageListenAll(state)
                : _buildStageTransition(
                  image: '$assetImageMainLesson/speaking_arena/BG27.png',
                  title: 'Stage I',
                  subTitle: context.loc.stage1Speaking,
                ),
      );
    } else {
      return _buildStageListenAll(state);
    }
  }

  Widget _buildStageListenAll(SpeakingState state) {
    return Scaffold(
      appBar: MainLessonAppBar(
        title: context.loc.speakingArena,
        isBookmarked:
            viewState.value!.speakings.isNotEmpty
                ? viewState
                    .value!
                    .speakings[viewState.value!.selectedIndex]
                    .isBookmarked
                : false,
        onBookmark: () {
          viewModel.saveBookmark();
        },
        onHelp: () {
          customNav(
            context,
            RouterName.speakingArenaInstruction,
            isReplace: true,
            params: {'level': widget.level, 'chapter': widget.chapter},
          );
        },
      ),
      body: Stack(
        alignment: Alignment.center,
        children: [
          ListView(
            padding: const EdgeInsets.only(
              bottom:
                  120, // Adjusted for NextButton and potentially AudioPlayerWidget
            ),
            children: [
              const SizedBox(height: 24),
              if (state.questionLength > 0)
                DashProgressIndicator(
                  progress: state.selectedIndex,
                  totalLength: state.questionLength,
                ),
              const SizedBox(height: 24.0),
              if (state.speakings.isNotEmpty) _buildQAContainer(state),
              if (state.speakings.isNotEmpty) ...[
                const SizedBox(
                  height: 24.0,
                ), // Spacing before AudioPlayerWidget
                Padding(
                  // Added padding for the AudioPlayerWidget if needed
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Container(
                    width: double.infinity, // Takes full width of its parent
                    height: 58,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Color(0xffFFEDEB),
                      border: Border.all(
                        color: const Color(0xffE82329),
                        width: 0.4,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    child: SoundWaveAudioPlayer(
                      audioUrl: viewState.value!.audioUrl,
                      onComplete: () {
                        // Ensure we use the latest state if possible, or rely on viewmodel
                        // The 'state' here is the one passed to _buildStageListenAll
                        final currentSpeaking =
                            state.speakings[state.selectedIndex];
                        viewModel.setQAActive(
                          session:
                              currentSpeaking.question.isActive
                                  ? SpeakingSessionType.answer
                                  : SpeakingSessionType.question,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ],
          ),
          RepeatNextButton(
            onTapRepeat: () => viewModel.prevQuestion(),
            onTapNext: () => viewModel.nextQuestion(context),
            leftTitle: context.loc.previous,
            leftActive: state.selectedIndex > 0,
          ),
        ],
      ),
    );
  }

  Widget _buildQAContainer(SpeakingState state) {
    final currentSpeaking = state.speakings[state.selectedIndex];

    return Stack(
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: Color(0xffFFEDEB),
            border: Border.all(color: const Color(0xffE82329), width: 0.4),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: .12),
                blurRadius: 18.0,
                spreadRadius: -2.0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 18),
          margin: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              _buildQASection(
                prefix: 'Q',
                content: currentSpeaking.question,
                onTap:
                    () => viewModel.setQAActive(
                      session: SpeakingSessionType.question,
                    ),
                state: state,
              ),
              const SizedBox(height: 16.0),
              _buildQASection(
                prefix: 'A',
                content: currentSpeaking.answer,
                onTap:
                    () => viewModel.setQAActive(
                      session: SpeakingSessionType.answer,
                    ),
                state: state,
              ),
              // AudioPlayerWidget is removed from here
            ],
          ),
        ),
        Align(
          alignment: Alignment.topRight,
          child: Padding(
            padding: const EdgeInsets.only(right: 12),
            child: IconButton(
              icon: Icon(
                Icons.g_translate,
                size: 24,
                color: Color(0xff540005).withValues(alpha: 0.2),
              ),
              onPressed: () {
                viewModel.translate();
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQASection({
    required String prefix,
    required dynamic content, // Assuming content has 'isActive' and 'text'
    required VoidCallback onTap,
    required SpeakingState state,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.0),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$prefix${state.selectedIndex + 1}: ',
              style:
                  content.isActive
                      ? Theme.of(context).textTheme.titleLarge
                      : Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: const Color(0xff7F7573),
                      ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    viewState.value?.isTranslate == true
                        ? content.translation
                        : content.text,
                    style:
                        content.isActive
                            ? Theme.of(context).textTheme.titleLarge
                            : Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: const Color(0xff7F7573),
                            ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStageTransition({
    required String image,
    required String title,
    required String subTitle,
  }) {
    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(title, style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: 62),
          Container(
            width: 253,
            height: 253,
            margin: const EdgeInsets.symmetric(horizontal: 16.0),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image: AssetImage(image),
                fit: BoxFit.fill,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: .12),
                  blurRadius: 15.0,
                  spreadRadius: 1.0,
                  offset: const Offset(0, 6),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24.0),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Text(
              subTitle,
              style: Theme.of(context).textTheme.titleLarge,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
