import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/incomplete_challenge.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/score_progress_indicator.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/screenshot_share_button.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart';
import 'package:selfeng/shared/widgets/v_button_gradient.dart';
import 'package:screenshot/screenshot.dart';

class SpeakingArenaResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  final String stage;

  const SpeakingArenaResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
    required this.stage,
  });

  @override
  ConsumerState<SpeakingArenaResultScreen> createState() =>
      _SpeakingArenaResultScreenState();
}

class _SpeakingArenaResultScreenState
    extends ConsumerState<SpeakingArenaResultScreen>
    with TickerProviderStateMixin {
  late final AudioPlayer _bgmPlayer = AudioPlayer();
  // Create a screenshot controller
  final ScreenshotController _screenshotController = ScreenshotController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongratsSound() async {
    final isAudioEnabled = ref.read(audioToggleProvider);
    if (isAudioEnabled) {
      await _bgmPlayer.stop();
      try {
        await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
      } catch (e) {
        // Handle exception silently
      }
    }
  }

  void _navigateToNext() {
    int currentChapter = int.parse(widget.chapter.trim(), radix: 10);
    bool isLastChapter =
        widget.level == 'C2' && currentChapter == 7 || currentChapter % 8 == 0;

    if (isLastChapter) {
      _navigateToCertificateScreen();
    } else {
      _navigateToNextChapter();
    }
  }

  void _navigateToNextChapter() {
    customNav(
      context,
      RouterName.chapterTitle,
      params: {
        'level': widget.level,
        'chapter': (int.parse(widget.chapter.trim(), radix: 10) + 1).toString(),
      },
      isReplace: true,
    );
  }

  void _navigateToCertificateScreen() {
    customNav(
      context,
      RouterName.certificateNotification,
      params: {'level': widget.level},
      isReplace: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final provider = speakingControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      SpeakingStage.values.byName(widget.stage),
    );

    final asyncState = ref.watch(provider);
    final viewModel = ref.read(provider.notifier);

    return Screenshot(
      controller: _screenshotController,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: asyncState.when(
          // Loading State: Show a centered progress indicator
          loading:
              () => const Center(child: CircularProgressIndicator.adaptive()),
          error:
              (error, stackTrace) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    error.toString(),
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ),
          data:
              (state) => _buildSuccessContentWithLoadingCheck(
                context,
                state,
                viewModel,
              ),
        ),
      ),
    );
  }

  Widget _buildSuccessContentWithLoadingCheck(
    BuildContext context,
    SpeakingState state,
    SpeakingController viewModel,
  ) {
    if (widget.stage == 'stage3') {
      final pathLength = viewModel.paths.length;
      final resultStage2 = state.resultStage2;
      final resultStage3 = state.resultStage3;

      // Handle null results
      if (resultStage2 == null || resultStage3 == null) {
        debugPrint('ResultStage2 or ResultStage3 is null. Stage2: $resultStage2, Stage3: $resultStage3');
        return _buildErrorContent(context, 'Missing result data');
      }
      
      // Handle data count mismatch
      if (resultStage2.dataCount != pathLength || resultStage3.dataCount != pathLength) {
        debugPrint('Data count mismatch. PathLength: $pathLength, Stage2 count: ${resultStage2.dataCount}, Stage3 count: ${resultStage3.dataCount}');
        return _buildErrorContent(context, 'Incomplete data for results');
      }
    }

    return _buildSuccessContent(context, state, viewModel);
  }

  Widget _buildSuccessContent(
    BuildContext context,
    SpeakingState state,
    SpeakingController viewModel,
  ) {
    final resultStage2 = state.resultStage2;
    final resultStage3 = state.resultStage3;
    final pathLength = viewModel.paths.length;

    // ⬇️ Guard untuk null
    if (resultStage2 == null || resultStage3 == null) {
      return const Center(child: CircularProgressIndicator.adaptive());
    }

    final bool isStage2Complete = resultStage2.dataCount == pathLength;
    final bool isStage3Complete = resultStage3.dataCount == pathLength;

    if (isStage2Complete && isStage3Complete) {
      final stage2Score = resultStage2;
      final stage3Score = resultStage3;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        _playCongratsSound();
      });

      return DefaultTabController(
        length: 2,
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 82),
                _buildEvaluationHeader(context),
                const SizedBox(height: 24),
                _buildTabBar(context),
                Expanded(
                  child: _buildTabBarView(context, stage2Score, stage3Score),
                ),
                const SizedBox(height: 80),
              ],
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 24,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    VButtonGradient(
                      title: context.loc.next,
                      onTap: () => _navigateToNext(),
                      isBorder: false,
                    ),
                    SizedBox(height: 16),
                    ScreenshotShareButton(
                      title: 'Chapter ${widget.chapter} Speaking Arena',
                      screenshotController: _screenshotController,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return const IncompleteChallenge(
        assetImagePath: 'assets/images/main_lesson/notcomplete2.png',
      );
    }
  }

  Widget _buildErrorContent(BuildContext context, String errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'Error Loading Results',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 16),
            TextButton(
              onPressed: () {
                // Allow user to retry or navigate back
                Navigator.of(context).pop();
              },
              child: Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEvaluationHeader(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      // decoration: BoxDecoration(
      //   gradient: const LinearGradient(
      //     colors: [Color(0xff682000), Color(0xff490206)],
      //     begin: Alignment.bottomLeft,
      //     end: Alignment.topRight,
      //   ),
      //   borderRadius: BorderRadius.circular(8),
      // ),
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ), // Adjusted padding
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        '${context.loc.chapter} ${widget.chapter}',
        style: Theme.of(
          context,
        ).textTheme.headlineSmall!.copyWith(color: Color(0xff680007)),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return TabBar(
      indicatorSize: TabBarIndicatorSize.tab,
      dividerColor: Colors.transparent,
      labelStyle: Theme.of(context).textTheme.titleMedium,
      unselectedLabelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .6),
      ),
      tabs: <Widget>[
        Tab(text: '${context.loc.stage} II'),
        Tab(text: '${context.loc.stage} III'),
      ],
    );
  }

  Widget _buildTabBarView(
    BuildContext context,
    SpeakingAgregateScore resultStage2,
    SpeakingAgregateScore resultStage3,
  ) {
    return TabBarView(
      children: <Widget>[
        _SpeakingResultDetailsView(score: resultStage2),
        _SpeakingResultDetailsView(score: resultStage3),
      ],
    );
  }
}

class _SpeakingResultDetailsView extends StatelessWidget {
  final SpeakingAgregateScore score;

  const _SpeakingResultDetailsView({required this.score});

  int _calculateScore(double scoreValue, int dataCount) {
    if (dataCount == 0 || scoreValue.isNaN || scoreValue.isInfinite) {
      return 0;
    }
    return (scoreValue / dataCount).round();
  }

  int _calculateAverage(List<int> scoreValues) {
    if (scoreValues.isEmpty) {
      return 0;
    }
    final total = scoreValues.fold(0, (sum, value) => sum + value);
    return (total / scoreValues.length).round();
  }

  @override
  Widget build(BuildContext context) {
    // Calculate individual scores for clarity
    final accuracyScore = _calculateScore(score.accuracyScore, score.dataCount);
    final fluencyScore = _calculateScore(score.fluencyScore, score.dataCount);
    final rhythmScore = _calculateScore(
      score.prosodyScore,
      score.dataCount,
    ); // Renamed for clarity

    final averageScore = _calculateAverage([
      accuracyScore,
      fluencyScore,
      rhythmScore,
    ]);

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          TweenAnimationBuilder<int>(
            tween: IntTween(begin: 0, end: averageScore),
            duration: const Duration(milliseconds: 1800),
            builder: (context, value, child) {
              return Text(
                '$value',
                style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                  fontSize: 70,
                  color:
                      value > 90
                          ? const Color(0xff36AA34)
                          : value > 70
                          ? const Color(0xffF5BE48)
                          : const Color(0xff93000F),
                ),
              );
            },
          ),
          SizedBox(height: 8),
          TweenAnimationBuilder<int>(
            tween: IntTween(begin: 0, end: averageScore),
            duration: const Duration(milliseconds: 1800),
            builder: (context, value, child) {
              final title =
                  value > 90
                      ? context.loc.excellent
                      : value > 70
                      ? context.loc.good
                      : context.loc.needs_practice;
              return Text(
                title,
                style: Theme.of(context).textTheme.headlineLarge,
              );
            },
          ),
          TweenAnimationBuilder<int>(
            tween: IntTween(begin: 0, end: averageScore),
            duration: const Duration(milliseconds: 1800),
            builder: (context, value, child) {
              final desc =
                  value > 90
                      ? context.loc.excellent_desc
                      : value > 70
                      ? context.loc.good_desc
                      : context.loc.needs_practice_desc;
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  desc,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    color: const Color(0xffB4A9A7),
                  ),
                ),
              );
            },
          ),
          SizedBox(height: 24),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            width: MediaQuery.of(context).size.width - 48,
            child: Wrap(
              direction: Axis.horizontal,
              alignment: WrapAlignment.spaceBetween,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                _scoreValue(
                  score: accuracyScore,
                  description: context.loc.sound_match,
                  context: context,
                ),
                _scoreValue(
                  score: fluencyScore,
                  description: context.loc.smooth_talk,
                  context: context,
                ),
                _scoreValue(
                  score: rhythmScore,
                  description: context.loc.natural_flow,
                  context: context,
                ),
              ],
            ),
          ), // Bottom padding
        ],
      ),
    );
  }

  Widget _scoreValue({
    required int score,
    required String description,
    required BuildContext context,
  }) {
    return Column(
      children: [
        ScoreProgressIndicator(score: score),
        SizedBox(height: 16),
        Text(description, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }
}
