import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:selfeng/configs/routes/core_router_name.dart';
import 'package:selfeng/features/main_lesson/domain/models/main_lesson.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/speaking_controller.dart';
import 'package:selfeng/features/main_lesson/presentation/providers/state/speaking_state.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/incomplete_challenge.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/score_progress_indicator.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/screenshot_share_button.dart';
import 'package:selfeng/shared/domain/models/user-data/user_data.dart';
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart';
import 'package:selfeng/services/crashlytics_service/domain/providers/crashlytics_service_provider.dart';
import 'package:selfeng/shared/widgets/v_button_gradient.dart';
import 'package:screenshot/screenshot.dart';

class SpeakingArenaResultScreen extends ConsumerStatefulWidget {
  final String level;
  final String chapter;
  final String path;
  final String stage;

  const SpeakingArenaResultScreen({
    super.key,
    required this.level,
    required this.chapter,
    required this.path,
    required this.stage,
  });

  @override
  ConsumerState<SpeakingArenaResultScreen> createState() =>
      _SpeakingArenaResultScreenState();
}

class _SpeakingArenaResultScreenState
    extends ConsumerState<SpeakingArenaResultScreen>
    with TickerProviderStateMixin {
  late final AudioPlayer _bgmPlayer = AudioPlayer();
  // Create a screenshot controller
  final ScreenshotController _screenshotController = ScreenshotController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _bgmPlayer.dispose();
    super.dispose();
  }

  Future<void> _playCongratsSound() async {
    final isAudioEnabled = ref.read(audioToggleProvider);
    if (isAudioEnabled) {
      await _bgmPlayer.stop();
      try {
        await _bgmPlayer.play(AssetSource('sounds/score.mp3'));
      } catch (e) {
        // Handle exception silently
      }
    }
  }

  void _navigateToNext() {
    int currentChapter = int.parse(widget.chapter.trim(), radix: 10);
    bool isLastChapter =
        widget.level == 'C2' && currentChapter == 7 || currentChapter % 8 == 0;

    if (isLastChapter) {
      _navigateToCertificateScreen();
    } else {
      _navigateToNextChapter();
    }
  }

  void _navigateToNextChapter() {
    customNav(
      context,
      RouterName.chapterTitle,
      params: {
        'level': widget.level,
        'chapter': (int.parse(widget.chapter.trim(), radix: 10) + 1).toString(),
      },
      isReplace: true,
    );
  }

  void _navigateToCertificateScreen() {
    customNav(
      context,
      RouterName.certificateNotification,
      params: {'level': widget.level},
      isReplace: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    final provider = speakingControllerProvider(
      widget.level,
      widget.chapter,
      widget.path,
      SpeakingStage.values.byName(widget.stage),
    );

    final asyncState = ref.watch(provider);
    final viewModel = ref.read(provider.notifier);

    return Screenshot(
      controller: _screenshotController,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: asyncState.when(
          // Loading State: Show a centered progress indicator
          loading:
              () => const Center(child: CircularProgressIndicator.adaptive()),
          error:
              (error, stackTrace) => Center(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    error.toString(),
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ),
          data:
              (state) => _buildSuccessContentWithLoadingCheck(
                context,
                state,
                viewModel,
              ),
        ),
      ),
    );
  }

  Widget _buildSuccessContentWithLoadingCheck(
    BuildContext context,
    SpeakingState state,
    SpeakingController viewModel,
  ) {
    try {
      // Show loading indicator if state is still loading
      if (state.isLoading) {
        return const Center(child: CircularProgressIndicator.adaptive());
      }

      if (widget.stage == 'stage3') {
        final requiredPaths =
            viewModel.paths.map((pathData) => pathData.contentPath).toSet();
        final resultStage2 = state.resultStage2;
        final resultStage3 = state.resultStage3;

        // Handle null results with crash reporting
        if (resultStage2 == null || resultStage3 == null) {
          _reportCompletionError(
            'Missing result data for speaking arena stages',
            context: {
              'stage2_null': resultStage2 == null,
              'stage3_null': resultStage3 == null,
              'required_paths_count': requiredPaths.length,
              'level': widget.level,
              'chapter': widget.chapter,
            },
          );
          return _buildErrorContent(context, 'Missing result data');
        }

        // Get completed paths from viewModel
        final stage2CompletedPaths = viewModel.stageTwoCompletedPaths;
        final stage3CompletedPaths = viewModel.stageThreeCompletedPaths;

        // Check if completed paths contain null values
        if (stage2CompletedPaths.contains(null) ||
            stage3CompletedPaths.contains(null)) {
          _reportCompletionError(
            'Null values found in completed paths collections',
            context: {
              'stage2_has_null': stage2CompletedPaths.contains(null),
              'stage3_has_null': stage3CompletedPaths.contains(null),
              'required_paths_count': requiredPaths.length,
              'level': widget.level,
              'chapter': widget.chapter,
            },
          );
          return _buildErrorContent(context, 'Invalid completion data');
        }

        // Verify that ALL required paths are completed for both stages
        final stage2MissingPaths = requiredPaths.difference(
          stage2CompletedPaths,
        );
        final stage3MissingPaths = requiredPaths.difference(
          stage3CompletedPaths,
        );

        if (stage2MissingPaths.isNotEmpty || stage3MissingPaths.isNotEmpty) {
          _reportCompletionError(
            'Incomplete path completion detected',
            context: {
              'stage2_missing_count': stage2MissingPaths.length,
              'stage3_missing_count': stage3MissingPaths.length,
              'stage2_missing_paths': stage2MissingPaths.toList(),
              'stage3_missing_paths': stage3MissingPaths.toList(),
              'required_paths_count': requiredPaths.length,
              'level': widget.level,
              'chapter': widget.chapter,
            },
          );
          return _buildErrorContent(context, 'Incomplete lesson progress');
        }

        // Additional validation: Check data count consistency
        if (resultStage2.dataCount != requiredPaths.length ||
            resultStage3.dataCount != requiredPaths.length) {
          _reportCompletionError(
            'Data count mismatch with required paths',
            context: {
              'required_paths_count': requiredPaths.length,
              'stage2_data_count': resultStage2.dataCount,
              'stage3_data_count': resultStage3.dataCount,
              'level': widget.level,
              'chapter': widget.chapter,
            },
          );
          return _buildErrorContent(context, 'Data inconsistency detected');
        }
      }

      return _buildSuccessContent(context, state, viewModel);
    } catch (error, stackTrace) {
      // Catch any unexpected errors and report them
      _reportCompletionError(
        'Unexpected error in success content loading check',
        error: error,
        stackTrace: stackTrace,
        context: {
          'stage': widget.stage,
          'level': widget.level,
          'chapter': widget.chapter,
        },
      );
      return _buildErrorContent(context, 'An unexpected error occurred');
    }
  }

  Widget _buildSuccessContent(
    BuildContext context,
    SpeakingState state,
    SpeakingController viewModel,
  ) {
    try {
      final resultStage2 = state.resultStage2;
      final resultStage3 = state.resultStage3;
      final requiredPaths =
          viewModel.paths.map((pathData) => pathData.contentPath).toSet();

      // Show loading indicator while data is still being processed
      if (resultStage2 == null || resultStage3 == null) {
        return const Center(child: CircularProgressIndicator.adaptive());
      }

      // Use the improved completion logic based on actual path completion
      final stage2CompletedPaths = viewModel.stageTwoCompletedPaths;
      final stage3CompletedPaths = viewModel.stageThreeCompletedPaths;

      final bool isStage2Complete =
          requiredPaths.difference(stage2CompletedPaths).isEmpty;
      final bool isStage3Complete =
          requiredPaths.difference(stage3CompletedPaths).isEmpty;

      if (isStage2Complete && isStage3Complete) {
        final stage2Score = resultStage2;
        final stage3Score = resultStage3;

        WidgetsBinding.instance.addPostFrameCallback((_) {
          _playCongratsSound();
        });

        return DefaultTabController(
          length: 2,
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  const SizedBox(height: 82),
                  _buildEvaluationHeader(context),
                  const SizedBox(height: 24),
                  _buildTabBar(context),
                  Expanded(
                    child: _buildTabBarView(context, stage2Score, stage3Score),
                  ),
                  const SizedBox(height: 80),
                ],
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 24,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      VButtonGradient(
                        title: context.loc.next,
                        onTap: () => _navigateToNext(),
                        isBorder: false,
                      ),
                      SizedBox(height: 16),
                      ScreenshotShareButton(
                        title: 'Chapter ${widget.chapter} Speaking Arena',
                        screenshotController: _screenshotController,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      } else {
        return const IncompleteChallenge(
          assetImagePath: 'assets/images/main_lesson/notcomplete2.png',
        );
      }
    } catch (error, stackTrace) {
      // Handle any unexpected errors in success content building
      _reportCompletionError(
        'Unexpected error in success content building',
        error: error,
        stackTrace: stackTrace,
        context: {
          'stage': widget.stage,
          'level': widget.level,
          'chapter': widget.chapter,
        },
      );
      return _buildErrorContent(context, 'An unexpected error occurred');
    }
  }

  Widget _buildErrorContent(BuildContext context, String errorMessage) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 48,
            ),
            SizedBox(height: 16),
            Text(
              'Error Loading Results',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            SizedBox(height: 8),
            Text(
              errorMessage,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 16),
            TextButton(
              onPressed: () {
                // Allow user to retry or navigate back
                Navigator.of(context).pop();
              },
              child: Text('Go Back'),
            ),
          ],
        ),
      ),
    );
  }

  /// Report completion errors to Firebase Crashlytics with detailed context
  Future<void> _reportCompletionError(
    String message, {
    dynamic error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    try {
      final crashlytics = ref.read(crashlyticsServiceProvider);
      await crashlytics.recordError(
        error ?? Exception(message),
        stackTrace ?? StackTrace.current,
        reason: 'Speaking Arena Result Screen Error',
        context: {
          'category': 'speaking_arena_result',
          'operation': 'completion_check',
          'screen': 'speaking_arena_result_screen',
          'timestamp': DateTime.now().toIso8601String(),
          if (context != null) ...context,
        },
        fatal: false,
      );
    } catch (e) {
      // Don't let error reporting errors break the app
      debugPrint('Failed to report completion error: $e');
    }
  }

  Widget _buildEvaluationHeader(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      // decoration: BoxDecoration(
      //   gradient: const LinearGradient(
      //     colors: [Color(0xff682000), Color(0xff490206)],
      //     begin: Alignment.bottomLeft,
      //     end: Alignment.topRight,
      //   ),
      //   borderRadius: BorderRadius.circular(8),
      // ),
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ), // Adjusted padding
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        '${context.loc.chapter} ${widget.chapter}',
        style: Theme.of(
          context,
        ).textTheme.headlineSmall!.copyWith(color: Color(0xff680007)),
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return TabBar(
      indicatorSize: TabBarIndicatorSize.tab,
      dividerColor: Colors.transparent,
      labelStyle: Theme.of(context).textTheme.titleMedium,
      unselectedLabelStyle: Theme.of(context).textTheme.titleMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: .6),
      ),
      tabs: <Widget>[
        Tab(text: '${context.loc.stage} II'),
        Tab(text: '${context.loc.stage} III'),
      ],
    );
  }

  Widget _buildTabBarView(
    BuildContext context,
    SpeakingAgregateScore resultStage2,
    SpeakingAgregateScore resultStage3,
  ) {
    return TabBarView(
      children: <Widget>[
        _SpeakingResultDetailsView(score: resultStage2),
        _SpeakingResultDetailsView(score: resultStage3),
      ],
    );
  }
}

class _SpeakingResultDetailsView extends StatelessWidget {
  final SpeakingAgregateScore score;

  const _SpeakingResultDetailsView({required this.score});

  int _calculateScore(double scoreValue, int dataCount) {
    if (dataCount == 0 || scoreValue.isNaN || scoreValue.isInfinite) {
      return 0;
    }
    return (scoreValue / dataCount).round();
  }

  int _calculateAverage(List<int> scoreValues) {
    if (scoreValues.isEmpty) {
      return 0;
    }
    final total = scoreValues.fold(0, (sum, value) => sum + value);
    return (total / scoreValues.length).round();
  }

  @override
  Widget build(BuildContext context) {
    // Calculate individual scores for clarity
    final accuracyScore = _calculateScore(score.accuracyScore, score.dataCount);
    final fluencyScore = _calculateScore(score.fluencyScore, score.dataCount);
    final rhythmScore = _calculateScore(
      score.prosodyScore,
      score.dataCount,
    ); // Renamed for clarity

    final averageScore = _calculateAverage([
      accuracyScore,
      fluencyScore,
      rhythmScore,
    ]);

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          TweenAnimationBuilder<int>(
            tween: IntTween(begin: 0, end: averageScore),
            duration: const Duration(milliseconds: 1800),
            builder: (context, value, child) {
              return Text(
                '$value',
                style: Theme.of(context).textTheme.headlineLarge!.copyWith(
                  fontSize: 70,
                  color:
                      value > 90
                          ? const Color(0xff36AA34)
                          : value > 70
                          ? const Color(0xffF5BE48)
                          : const Color(0xff93000F),
                ),
              );
            },
          ),
          SizedBox(height: 8),
          TweenAnimationBuilder<int>(
            tween: IntTween(begin: 0, end: averageScore),
            duration: const Duration(milliseconds: 1800),
            builder: (context, value, child) {
              final title =
                  value > 90
                      ? context.loc.excellent
                      : value > 70
                      ? context.loc.good
                      : context.loc.needs_practice;
              return Text(
                title,
                style: Theme.of(context).textTheme.headlineLarge,
              );
            },
          ),
          TweenAnimationBuilder<int>(
            tween: IntTween(begin: 0, end: averageScore),
            duration: const Duration(milliseconds: 1800),
            builder: (context, value, child) {
              final desc =
                  value > 90
                      ? context.loc.excellent_desc
                      : value > 70
                      ? context.loc.good_desc
                      : context.loc.needs_practice_desc;
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Text(
                  desc,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    color: const Color(0xffB4A9A7),
                  ),
                ),
              );
            },
          ),
          SizedBox(height: 24),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 24),
            width: MediaQuery.of(context).size.width - 48,
            child: Wrap(
              direction: Axis.horizontal,
              alignment: WrapAlignment.spaceBetween,
              crossAxisAlignment: WrapCrossAlignment.center,
              children: [
                _scoreValue(
                  score: accuracyScore,
                  description: context.loc.sound_match,
                  context: context,
                ),
                _scoreValue(
                  score: fluencyScore,
                  description: context.loc.smooth_talk,
                  context: context,
                ),
                _scoreValue(
                  score: rhythmScore,
                  description: context.loc.natural_flow,
                  context: context,
                ),
              ],
            ),
          ), // Bottom padding
        ],
      ),
    );
  }

  Widget _scoreValue({
    required int score,
    required String description,
    required BuildContext context,
  }) {
    return Column(
      children: [
        ScoreProgressIndicator(score: score),
        SizedBox(height: 16),
        Text(description, style: Theme.of(context).textTheme.bodySmall),
      ],
    );
  }
}
